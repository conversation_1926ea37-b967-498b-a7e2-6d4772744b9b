#include "../include/types.h"
#include "../include/x86.h"

#define SECTSIZE 512
#define ELFHDR ((struct elfhdr *)0x10000) // 临时存放ELF头的位置

// ELF文件格式
struct elfhdr
{
  uint magic; // 必须等于ELF_MAGIC
  uchar elf[12];
  ushort type;
  ushort machine;
  uint version;
  uint entry; // 程序入口点
  uint phoff; // 程序头表偏移
  uint shoff; // 节头表偏移
  uint flags;
  ushort ehsize;
  ushort phentsize;
  ushort phnum; // 程序头表条目数
  ushort shentsize;
  ushort shnum;
  ushort shstrndx;
};

#define ELF_MAGIC 0x464C457FU // "\x7FELF"

// 从磁盘读取一个扇区
void readsect(void *dst, uint offset)
{
  // 等待磁盘就绪
  while ((inb(0x1F7) & 0xC0) != 0x40)
    ;

  // 发出读取命令
  outb(0x1F2, 1); // 扇区数量
  outb(0x1F3, offset);
  outb(0x1F4, offset >> 8);
  outb(0x1F5, offset >> 16);
  outb(0x1F6, (offset >> 24) | 0xE0);
  outb(0x1F7, 0x20); // 命令: 读取扇区

  // 等待磁盘就绪
  while ((inb(0x1F7) & 0xC0) != 0x40)
    ;

  // 读取数据
  insl(0x1F0, dst, SECTSIZE / 4);
}

// 从磁盘读取count个字节到物理地址pa
void readseg(uchar *pa, uint count, uint offset)
{
  uchar *epa;
  epa = pa + count;

  // 从扇区边界开始读取
  pa -= offset % SECTSIZE;
  offset = (offset / SECTSIZE) + 1;

  // 读取数据
  for (; pa < epa; pa += SECTSIZE, offset++)
    readsect(pa, offset);
}

void bootmain(void)
{
  // 从磁盘读取内核的第一个扇区到内存地址0x10000
  readsect((void *)0x10000, 1);

  // 检查是否是有效的ELF文件
  if (ELFHDR->magic != ELF_MAGIC)
    return; // 不是有效的ELF文件

  // 直接跳转到内核入口点
  ((void (*)(void))0x10000)();

  // 如果返回，进入无限循环
  while (1)
    ;
}
