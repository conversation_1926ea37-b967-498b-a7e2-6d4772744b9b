#include "../include/types.h"
#include "../include/defs.h"
#include "../include/trap.h"
#include "../include/syscall.h"
#include "../include/x86.h"
#include "../include/proc.h"

// 中断描述符表
struct gatedesc idt[256];

// 外部汇编函数声明
extern uint vectors[]; // 在trapasm.S中定义

// 设置中断门
static void setgate(struct gatedesc *gate, uint istrap, uint sel, uint off, uint dpl)
{
    gate->off_15_0 = (uint)off;
    gate->cs = sel;
    gate->args = 0;
    gate->rsv1 = 0;
    gate->type = istrap ? STS_TG32 : STS_IG32;
    gate->s = 0;
    gate->dpl = dpl;
    gate->p = 1;
    gate->off_31_16 = (uint)off >> 16;
}

// 初始化中断处理
void trap_init(void)
{
    int i;

    // 设置异常处理程序
    for (i = 0; i < 256; i++)
    {
        setgate(&idt[i], 0, 8, vectors[i], DPL_KERNEL);
    }

    // 系统调用使用用户特权级
    setgate(&idt[T_SYSCALL], 1, 8, vectors[T_SYSCALL], DPL_USER);

    // 加载IDT
    struct
    {
        ushort limit;
        uint base;
    } __attribute__((packed)) idtr;

    idtr.limit = sizeof(idt) - 1;
    idtr.base = (uint)idt;
    lidt(&idtr);

    cprintf("Trap handling initialized\n");
}

// 中断处理程序
void trap_handler(struct trapframe *tf)
{
    switch (tf->trapno)
    {
    case T_SYSCALL:
        // 系统调用处理
        tf->eax = syscall_handler(tf->eax, tf->ebx, tf->ecx, tf->edx, tf->esi);
        break;

    case T_DIVIDE:
        cprintf("Divide by zero error!\n");
        if (current_proc)
        {
            cprintf("Process %d killed\n", current_proc->pid);
            proc_exit(-1);
        }
        else
        {
            panic("Divide by zero in kernel");
        }
        break;

    case T_GPFLT:
        cprintf("General protection fault!\n");
        cprintf("Error code: 0x%x\n", tf->err);
        if (current_proc)
        {
            cprintf("Process %d killed\n", current_proc->pid);
            proc_exit(-1);
        }
        else
        {
            panic("General protection fault in kernel");
        }
        break;

    case T_PGFLT:
        cprintf("Page fault!\n");
        cprintf("Error code: 0x%x\n", tf->err);
        if (current_proc)
        {
            cprintf("Process %d killed\n", current_proc->pid);
            proc_exit(-1);
        }
        else
        {
            panic("Page fault in kernel");
        }
        break;

    default:
        cprintf("Unexpected trap %d\n", tf->trapno);
        if (current_proc)
        {
            cprintf("Process %d killed\n", current_proc->pid);
            proc_exit(-1);
        }
        else
        {
            panic("Unexpected trap in kernel");
        }
        break;
    }
}
