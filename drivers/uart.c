#include "../include/types.h"
#include "../include/x86.h"
#include "../include/defs.h"
#include "../include/uart.h"

// 初始化串行端口
void uartinit(void)
{
  // 关闭中断
  outb(COM1 + UART_IER, 0x00);

  // 设置波特率
  outb(COM1 + UART_LCR, 0x80); // 解锁除数寄存器
  outb(COM1 + UART_DLL, 115200 / 9600);
  outb(COM1 + UART_DLM, 0);

  // 8位数据位，无奇偶校验，1位停止位
  outb(COM1 + UART_LCR, 0x03);

  // 启用FIFO，清除接收和发送FIFO
  outb(COM1 + UART_FCR, 0xC7);

  // 启用中断，RTS/DSR设置
  outb(COM1 + UART_MCR, 0x0B);

  cprintf("UART initialized.\n");
}

// 向串行端口输出一个字符
void uartputc(int c)
{
  // 等待发送缓冲区为空
  for (int i = 0; i < 128 && !(inb(COM1 + UART_LSR) & UART_LSR_THRE); i++)
    ;

  outb(COM1 + UART_TX, c);
}

// 从串行端口读取一个字符
int uartgetc(void)
{
  // 检查是否有数据可读
  if (!(inb(COM1 + UART_LSR) & UART_LSR_DR))
    return -1;

  // 读取数据
  return inb(COM1 + UART_RX);
}

// 轮询串行输入
void uartpoll(void)
{
  int c;

  c = uartgetc();
  if (c != -1)
  {
    // 输出字符到控制台
    cprintf("Serial input:\n");
    consputc(c);
    cprintf("\n");
  }
}
