#ifndef _DEFS_H_
#define _DEFS_H_

#include "types.h"

// 前向声明
struct proc;
struct trapframe;

// console.c
void consoleinit(void);
void cprintf(char *fmt, ...);
void consoleintr(int (*getc)(void));
void panic(char *) __attribute__((noreturn));
void consputc(int c); // 添加控制台输出单个字符的函数声明

// uart.c
void uartinit(void);
void uartputc(int c);
int uartgetc(void);
void uartpoll(void);

// main.c
void kmain(void);

// proc.c
void proc_init(void);
struct proc *proc_alloc(void);
void proc_free(struct proc *p);
int proc_load(struct proc *p, void *binary, uint size);
void proc_run(struct proc *p);
void proc_exit(int status);

// trap.c
void trap_init(void);
void trap_handler(struct trapframe *tf);

// syscall.c
void syscall_init(void);
int syscall_handler(uint syscall_num, uint arg1, uint arg2, uint arg3, uint arg4);

// batch.c
void batch_init(void);
void batch_run(void);

// elf.c
int elf_load(struct proc *p, void *binary, uint size);

#endif // _DEFS_H_
